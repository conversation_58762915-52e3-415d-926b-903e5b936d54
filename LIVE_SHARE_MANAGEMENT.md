# Live Share Management

This document explains how to manage the live share functionality in the L2M Boss Timer application.

## Current Status: 🔴 DISABLED

The live share functionality has been temporarily disabled to focus on core timer features.

## Quick Toggle

Use the toggle script to easily enable/disable live share:

```bash
# Check current status
node toggle-live-share.js status

# Disable live share (current state)
node toggle-live-share.js disable

# Enable live share
node toggle-live-share.js enable
```

## What Gets Disabled/Enabled

### When DISABLED (Current State):
- ❌ Live Share button is hidden
- ❌ Real-time collaboration features are inactive
- ❌ Connection status display is hidden
- ✅ Uses standard `useTimer` and `useBossManager` hooks
- ✅ All core timer functionality works normally
- ✅ Import/Export functionality still available

### When ENABLED:
- ✅ Live Share button appears in the toolbar
- ✅ Real-time collaboration with Supabase backend
- ✅ Connection status shows connected users
- ✅ Uses collaborative `useCollaborativeTimer` and `useCollaborativeBossManager` hooks
- ✅ Cross-device synchronization
- ✅ Room-based sharing with passwords

## Manual Management

If you prefer to manually enable/disable, edit `src/components/BossTimer.tsx`:

### To Disable:
1. Comment out collaborative imports:
   ```typescript
   // import { useCollaborativeTimer } from '@/hooks/useCollaborativeTimer';
   // import { useCollaborativeBossManager } from '@/hooks/useCollaborativeBossManager';
   ```

2. Enable standard imports:
   ```typescript
   import { useTimer } from '@/hooks/useTimer';
   import { useBossManager } from '@/hooks/useBossManager';
   ```

3. Update hook usage:
   ```typescript
   const { ... } = useTimer();
   const { ... } = useBossManager();
   ```

4. Comment out Live Share button and modal components

### To Enable:
1. Reverse the above changes
2. Uncomment collaborative imports and components
3. Use collaborative hooks instead of standard ones

## Dependencies

### Live Share Dependencies (only needed when enabled):
- `@supabase/supabase-js` - Real-time database
- Supabase project with proper configuration
- Environment variables in `.env.local`

### Core Dependencies (always needed):
- React hooks for timer and boss management
- Local storage for persistence
- Standard UI components

## Troubleshooting

### If you encounter issues after toggling:

1. **TypeScript errors**: Run the toggle script again to ensure all changes were applied
2. **Import errors**: Check that all imports are properly commented/uncommented
3. **Runtime errors**: Clear browser cache and localStorage
4. **Supabase errors**: Ensure environment variables are set when enabling live share

### Reset to clean state:
```bash
# Disable live share
node toggle-live-share.js disable

# Clear any cached data
# (manually clear browser localStorage if needed)
```

## Files Involved

- `src/components/BossTimer.tsx` - Main component with toggle points
- `src/hooks/useCollaborativeTimer.ts` - Collaborative timer logic
- `src/hooks/useCollaborativeBossManager.ts` - Collaborative boss management
- `src/hooks/useTimer.ts` - Standard timer logic
- `src/hooks/useBossManager.ts` - Standard boss management
- `src/components/RealTimeShareModal.tsx` - Live share UI
- `src/components/ConnectionStatus.tsx` - Connection display
- `src/services/supabaseService.ts` - Backend integration

## Benefits of This Approach

✅ **Clean separation** - Core functionality is independent of live share  
✅ **Easy toggling** - One command to switch modes  
✅ **No code deletion** - All live share code is preserved  
✅ **Reduced complexity** - Simpler debugging when disabled  
✅ **Flexible deployment** - Can deploy with or without live share  

This approach allows you to focus on core timer features while keeping the live share functionality ready for future use.
