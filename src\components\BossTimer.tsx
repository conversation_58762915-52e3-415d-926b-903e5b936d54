'use client';

import { useState, useEffect } from 'react';
// import { useCollaborativeTimer } from '@/hooks/useCollaborativeTimer'; // TEMPORARILY DISABLED
// import { useCollaborativeBossManager } from '@/hooks/useCollaborativeBossManager'; // TEMPORARILY DISABLED
import { useTimer } from '@/hooks/useTimer';
import { useBossManager } from '@/hooks/useBossManager';
import { Boss } from '@/types/boss';
import BossDetailsModal from './BossDetailsModal';
import NotificationSettings from './NotificationSettings';
import AddBossModal from './AddBossModal';
import EditBossModal from './EditBossModal';
import ShareModal from './ShareModal';
import ImportModal from './ImportModal';
// import RealTimeShareModal from './RealTimeShareModal'; // TEMPORARILY DISABLED
// import ConnectionStatus from './ConnectionStatus'; // TEMPORARILY DISABLED

export default function BossTimer() {
  const { timerState, startTimer, stopTimer, resetTimer, getTimeRemaining, isRespawned, stopNotificationSound, isPlaying } = useTimer();
  const { allBosses, customBosses, addBoss, updateBoss, deleteBoss, isCustomBoss } = useBossManager();
  const [sortBy, setSortBy] = useState<'name' | 'level' | 'respawnTime' | 'location' | 'timeOfDeath' | 'timeRemaining'>('timeRemaining');
  const [filterActive, setFilterActive] = useState<'all' | 'active' | 'inactive'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showManualInput, setShowManualInput] = useState<string | null>(null);
  const [manualDateTime, setManualDateTime] = useState('');
  const [selectedBoss, setSelectedBoss] = useState<Boss | null>(null);
  const [showNotificationSettings, setShowNotificationSettings] = useState(false);
  const [showAddBossModal, setShowAddBossModal] = useState(false);
  const [showEditBossModal, setShowEditBossModal] = useState(false);
  const [editingBoss, setEditingBoss] = useState<Boss | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  // const [showRealTimeShareModal, setShowRealTimeShareModal] = useState(false); // TEMPORARILY DISABLED
  const [isClient, setIsClient] = useState(false);

  // Set client flag after mount and check for shared data in URL
  useEffect(() => {
    setIsClient(true);

    // Check for shared data in URL
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const shareCode = urlParams.get('share');

      if (shareCode) {
        // Auto-open import modal if share code is found
        setShowImportModal(true);
      }
    }
  }, []);

  const sortedAndFilteredBosses = allBosses
    .filter(boss => {
      // Filter by active status
      if (filterActive === 'active' && !timerState[boss.id]?.isActive) return false;
      if (filterActive === 'inactive' && timerState[boss.id]?.isActive) return false;

      // Filter by search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesName = boss.name.toLowerCase().includes(query);
        const matchesLocation = boss.location.toLowerCase().includes(query);
        if (!matchesName && !matchesLocation) return false;
      }

      return true;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'level':
          return a.level - b.level;
        case 'respawnTime':
          return a.respawnTime - b.respawnTime;
        case 'location':
          return a.location.localeCompare(b.location);
        case 'timeOfDeath':
          const aTime = timerState[a.id]?.lastKilled?.getTime() || 0;
          const bTime = timerState[b.id]?.lastKilled?.getTime() || 0;
          return bTime - aTime; // Most recent first
        case 'timeRemaining':
          // Sort by time remaining (active timers first, then by remaining time)
          const aState = timerState[a.id];
          const bState = timerState[b.id];

          // If neither has active timer, sort by name
          if (!aState?.isActive && !bState?.isActive) {
            return a.name.localeCompare(b.name);
          }

          // Active timers come first
          if (aState?.isActive && !bState?.isActive) return -1;
          if (!aState?.isActive && bState?.isActive) return 1;

          // Both have active timers, sort by remaining time (shortest first)
          if (aState?.nextRespawn && bState?.nextRespawn) {
            const aRemaining = aState.nextRespawn.getTime() - Date.now();
            const bRemaining = bState.nextRespawn.getTime() - Date.now();
            return aRemaining - bRemaining;
          }

          return 0;
        default:
          return 0;
      }
    });

  const handleStartTimer = (boss: Boss) => {
    startTimer(boss.id, boss.respawnTime);
  };

  const handleStartManualTimer = (boss: Boss) => {
    setShowManualInput(boss.id);
    // Set current time as default
    const now = new Date();
    const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
      .toISOString()
      .slice(0, 16);
    setManualDateTime(localDateTime);
  };

  const handleConfirmManualTimer = (boss: Boss) => {
    if (manualDateTime) {
      const killTime = new Date(manualDateTime);
      startTimer(boss.id, boss.respawnTime, killTime);
      setShowManualInput(null);
      setManualDateTime('');
    }
  };

  const handleCancelManualInput = () => {
    setShowManualInput(null);
    setManualDateTime('');
  };

  const handleStopTimer = (boss: Boss) => {
    stopTimer(boss.id);
  };

  const handleResetTimer = (boss: Boss) => {
    resetTimer(boss.id);
  };

  const handleEditBoss = (boss: Boss) => {
    setEditingBoss(boss);
    setShowEditBossModal(true);
  };

  const handleDeleteBoss = (boss: Boss) => {
    if (window.confirm(`Are you sure you want to delete "${boss.name}"? This action cannot be undone.`)) {
      const success = deleteBoss(boss.id);
      if (success) {
        // Also reset any active timer for this boss
        resetTimer(boss.id);
      } else {
        alert('Cannot delete default bosses. Only custom bosses can be deleted.');
      }
    }
  };

  const handleUpdateBoss = (updatedBoss: Boss) => {
    updateBoss(updatedBoss.id, updatedBoss);
    setShowEditBossModal(false);
    setEditingBoss(null);
  };

  const handleImportData = (data: {
    customBosses: Boss[];
    timerStates: Record<string, { isActive: boolean; lastKilled?: string; respawnHours?: number; [key: string]: unknown }>;
    sharedBy?: string;
    sharedAt: string;
  }) => {
    // Add imported custom bosses
    data.customBosses.forEach(boss => {
      addBoss(boss);
    });

    // Import timer states
    Object.entries(data.timerStates).forEach(([bossId, state]) => {
      if (state.isActive && state.lastKilled && state.respawnHours) {
        const killTime = new Date(state.lastKilled);
        startTimer(bossId, state.respawnHours, killTime);
      }
    });

    // Show success message
    alert(`Successfully imported ${data.customBosses.length} custom bosses and timer states!${data.sharedBy ? ` (Shared by: ${data.sharedBy})` : ''}`);
  };

  const getStatusColor = (boss: Boss) => {
    const state = timerState[boss.id];
    if (!state?.isActive) return 'bg-gray-100 dark:bg-gray-800';
    if (isRespawned(boss.id)) return 'bg-green-100 dark:bg-green-900';

    // Check for warning states
    if (state.nextRespawn) {
      const timeUntilRespawn = state.nextRespawn.getTime() - new Date().getTime();
      const minutesUntilRespawn = Math.floor(timeUntilRespawn / (1000 * 60));

      if (minutesUntilRespawn <= 5) return 'bg-red-100 dark:bg-red-900';
      if (minutesUntilRespawn <= 30) return 'bg-yellow-100 dark:bg-yellow-900';
    }

    return 'bg-blue-100 dark:bg-blue-900';
  };

  const getStatusText = (boss: Boss) => {
    const state = timerState[boss.id];
    if (!state?.isActive) return 'Inactive';
    if (isRespawned(boss.id)) return 'Respawned!';

    // Check for warning states
    if (state.nextRespawn) {
      const timeUntilRespawn = state.nextRespawn.getTime() - new Date().getTime();
      const minutesUntilRespawn = Math.floor(timeUntilRespawn / (1000 * 60));

      if (minutesUntilRespawn <= 5) return 'Soon!';
      if (minutesUntilRespawn <= 30) return 'Warning';
    }

    return 'Active';
  };

  const formatTimeOfDeath = (boss: Boss) => {
    const state = timerState[boss.id];
    if (!state?.lastKilled) return '-';

    // Format the actual time
    const killTime = state.lastKilled;
    const timeString = killTime.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
    const dateString = killTime.toLocaleDateString([], {
      month: 'short',
      day: 'numeric'
    });

    // Calculate relative time
    const now = new Date();
    const timeDiff = now.getTime() - killTime.getTime();
    const hours = Math.floor(timeDiff / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

    let relativeTime = '';
    if (hours > 0) {
      relativeTime = `${hours}h ${minutes}m ago`;
    } else if (minutes > 0) {
      relativeTime = `${minutes}m ago`;
    } else {
      relativeTime = 'Just now';
    }

    // Show today's date as just time, otherwise include date
    const today = new Date();
    const isToday = killTime.toDateString() === today.toDateString();

    if (isToday) {
      return (
        <div className="text-xs">
          <div className="font-medium">{timeString}</div>
          <div className="text-gray-500 dark:text-gray-400">{relativeTime}</div>
        </div>
      );
    } else {
      return (
        <div className="text-xs">
          <div className="font-medium">{dateString} {timeString}</div>
          <div className="text-gray-500 dark:text-gray-400">{relativeTime}</div>
        </div>
      );
    }
  };



  if (!isClient) {
    return (
      <div className="space-y-6">
        <div className="flex justify-center items-center py-12">
          <div className="text-gray-600 dark:text-gray-400">Loading boss timer...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow space-y-4">
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
          <div className="flex flex-wrap gap-4">
            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Search
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search by name or location..."
                  className="pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white w-64"
                />
                <svg className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>

            {/* Sort by */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Sort by
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as typeof sortBy)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="timeRemaining">Time Remaining</option>
                <option value="name">Name</option>
                <option value="level">Level</option>
                <option value="respawnTime">Respawn Time</option>
                <option value="location">Location</option>
                <option value="timeOfDeath">Time of Death</option>
              </select>
            </div>

            {/* Filter by status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Status
              </label>
              <select
                value={filterActive}
                onChange={(e) => setFilterActive(e.target.value as typeof filterActive)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="all">All</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          <div className="flex gap-2">
            {/* Add Boss Button */}
            <button
              onClick={() => setShowAddBossModal(true)}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-md transition-colors flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Add Boss
            </button>

            {/* Stop Sound Button - only show when sound is playing */}
            {isPlaying && (
              <button
                onClick={stopNotificationSound}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-md transition-colors flex items-center gap-2 animate-pulse"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-6.219-8.56" />
                </svg>
                Stop Sound
              </button>
            )}

            {/* Share Button */}
            <button
              onClick={() => setShowShareModal(true)}
              className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white text-sm rounded-md transition-colors flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
              Share
            </button>

            {/* Real-Time Share Button - TEMPORARILY DISABLED */}
            {/*
            <button
              onClick={() => setShowRealTimeShareModal(true)}
              className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm rounded-md transition-colors flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              Live Share
            </button>
            */}

            {/* Import Button */}
            <button
              onClick={() => setShowImportModal(true)}
              className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white text-sm rounded-md transition-colors flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
              </svg>
              Import
            </button>

            {/* Notification Settings Button */}
            <button
              onClick={() => setShowNotificationSettings(true)}
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-md transition-colors flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2z" />
              </svg>
              Notifications
            </button>
          </div>
        </div>
      </div>

      {/* Summary */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow text-center">
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {allBosses.length}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">Total Bosses</div>
        </div>
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow text-center">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {Object.values(timerState).filter(state => state.isActive).length}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">Active Timers</div>
        </div>
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow text-center">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {allBosses.filter(boss => isRespawned(boss.id)).length}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">Ready to Hunt</div>
        </div>
      </div>

      {/* Boss Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Location
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Boss
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Level
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Respawn Time
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Time Remaining
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Time of Death
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
              {sortedAndFilteredBosses.map((boss) => (
                <tr key={boss.id} className={`${getStatusColor(boss)} transition-colors hover:bg-opacity-80`}>
                  <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                    {boss.location}
                  </td>
                  <td className="px-4 py-4 text-sm">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setSelectedBoss(boss)}
                        className="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
                      >
                        {boss.name}
                      </button>
                      {isCustomBoss(boss.id) && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200">
                          Custom
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                    Lv. {boss.level}
                  </td>
                  <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                    {boss.respawnTime}h
                    {boss.respawnVariance && (
                      <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
                        (±{boss.respawnVariance}m)
                      </span>
                    )}
                  </td>
                  <td className="px-4 py-4 text-sm">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      !timerState[boss.id]?.isActive
                        ? 'bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200'
                        : isRespawned(boss.id)
                        ? 'bg-green-200 text-green-800 dark:bg-green-800 dark:text-green-200'
                        : 'bg-blue-200 text-blue-800 dark:bg-blue-800 dark:text-blue-200'
                    }`}>
                      {getStatusText(boss)}
                    </span>
                  </td>
                  <td className="px-4 py-4 text-sm font-mono text-gray-900 dark:text-white">
                    {getTimeRemaining(boss.id) || '-'}
                  </td>
                  <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                    {formatTimeOfDeath(boss)}
                  </td>
                  <td className="px-4 py-4 text-sm">
                    {showManualInput === boss.id ? (
                      <div className="space-y-2">
                        <input
                          type="datetime-local"
                          value={manualDateTime}
                          onChange={(e) => setManualDateTime(e.target.value)}
                          className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        />
                        <div className="flex space-x-1">
                          <button
                            onClick={() => handleConfirmManualTimer(boss)}
                            className="px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded transition-colors"
                          >
                            ✓
                          </button>
                          <button
                            onClick={handleCancelManualInput}
                            className="px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded transition-colors"
                          >
                            ✕
                          </button>
                        </div>
                      </div>
                    ) : !timerState[boss.id]?.isActive ? (
                      <div className="space-y-1">
                        <div className="flex flex-col space-y-1">
                          <button
                            onClick={() => handleStartTimer(boss)}
                            className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded-md transition-colors"
                          >
                            Start Now
                          </button>
                          <button
                            onClick={() => handleStartManualTimer(boss)}
                            className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-md transition-colors"
                          >
                            Set Time
                          </button>
                        </div>
                        {/* Edit/Delete buttons for custom bosses */}
                        {isCustomBoss(boss.id) && (
                          <div className="flex flex-col space-y-1 pt-1 border-t border-gray-200 dark:border-gray-600">
                            <button
                              onClick={() => handleEditBoss(boss)}
                              className="px-3 py-1 bg-orange-600 hover:bg-orange-700 text-white text-xs rounded-md transition-colors"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => handleDeleteBoss(boss)}
                              className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded-md transition-colors"
                            >
                              Delete
                            </button>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="space-y-1">
                        <div className="flex flex-col space-y-1">
                          <button
                            onClick={() => handleStopTimer(boss)}
                            className="px-3 py-1 bg-yellow-600 hover:bg-yellow-700 text-white text-xs rounded-md transition-colors"
                          >
                            Stop
                          </button>
                          <button
                            onClick={() => handleResetTimer(boss)}
                            className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded-md transition-colors"
                          >
                            Reset
                          </button>
                        </div>
                        {/* Edit/Delete buttons for custom bosses */}
                        {isCustomBoss(boss.id) && (
                          <div className="flex flex-col space-y-1 pt-1 border-t border-gray-200 dark:border-gray-600">
                            <button
                              onClick={() => handleEditBoss(boss)}
                              className="px-3 py-1 bg-orange-600 hover:bg-orange-700 text-white text-xs rounded-md transition-colors"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => handleDeleteBoss(boss)}
                              className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded-md transition-colors"
                            >
                              Delete
                            </button>
                          </div>
                        )}
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Connection Status - TEMPORARILY DISABLED */}
      {/* <ConnectionStatus lastAction={lastAction} lastBossAction={lastBossAction} /> */}

      {/* Notification Alert */}
      {isPlaying && (
        <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg flex items-center justify-between animate-pulse">
          <div className="flex items-center gap-2">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 12h6m-6 0a3 3 0 106 0m-6 0a3 3 0 116 0" />
            </svg>
            <span className="font-medium">Boss notification sound is playing!</span>
          </div>
          <button
            onClick={stopNotificationSound}
            className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors"
          >
            Stop Sound
          </button>
        </div>
      )}

      {/* Modals */}
      <BossDetailsModal
        boss={selectedBoss}
        isOpen={!!selectedBoss}
        onClose={() => setSelectedBoss(null)}
      />

      <AddBossModal
        isOpen={showAddBossModal}
        onClose={() => setShowAddBossModal(false)}
        onAddBoss={addBoss}
      />

      <EditBossModal
        isOpen={showEditBossModal}
        onClose={() => {
          setShowEditBossModal(false);
          setEditingBoss(null);
        }}
        onUpdateBoss={handleUpdateBoss}
        boss={editingBoss}
      />

      <ShareModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        customBosses={customBosses}
        timerStates={timerState}
      />

      <ImportModal
        isOpen={showImportModal}
        onClose={() => setShowImportModal(false)}
        onImport={handleImportData}
      />

      <NotificationSettings
        isOpen={showNotificationSettings}
        onClose={() => setShowNotificationSettings(false)}
      />

      {/* Real-Time Share Modal - TEMPORARILY DISABLED */}
      {/*
      <RealTimeShareModal
        isOpen={showRealTimeShareModal}
        onClose={() => setShowRealTimeShareModal(false)}
      />
      */}
    </div>
  );
}
