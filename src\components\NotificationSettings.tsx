'use client';

import { useState } from 'react';
import { useNotifications } from '@/hooks/useNotifications';

interface NotificationSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function NotificationSettings({ isOpen, onClose }: NotificationSettingsProps) {
  const { settings, permission, requestPermission, updateSettings } = useNotifications();
  const [warningInput, setWarningInput] = useState('');

  if (!isOpen) return null;

  const handleAddWarning = () => {
    const minutes = parseInt(warningInput);
    if (minutes > 0 && !settings.warningMinutes.includes(minutes)) {
      updateSettings({
        warningMinutes: [...settings.warningMinutes, minutes].sort((a, b) => b - a)
      });
      setWarningInput('');
    }
  };

  const handleRemoveWarning = (minutes: number) => {
    updateSettings({
      warningMinutes: settings.warningMinutes.filter(m => m !== minutes)
    });
  };

  const handlePermissionRequest = async () => {
    await requestPermission();
  };

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4">
      {/* Background overlay */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />

      {/* Modal panel - Force light background for visibility */}
      <div
        className="relative w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white shadow-2xl rounded-2xl border-2 border-gray-300 p-6"
        style={{ backgroundColor: '#ffffff', zIndex: 10000 }}
      >
          {/* Header */}
          <div className="flex items-center justify-between mb-6 border-b border-gray-200 pb-4">
            <h3 className="text-2xl font-bold text-gray-900">
              Notification Settings
            </h3>
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors rounded-full hover:bg-gray-100"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="space-y-6">
            {/* Permission Status */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                Browser Permissions
              </h4>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Status: <span className={`font-medium ${
                      permission === 'granted' ? 'text-green-600' :
                      permission === 'denied' ? 'text-red-600' :
                      'text-yellow-600'
                    }`}>
                      {permission === 'granted' ? 'Allowed' :
                       permission === 'denied' ? 'Denied' : 'Not requested'}
                    </span>
                  </p>
                </div>
                {permission !== 'granted' && (
                  <button
                    onClick={handlePermissionRequest}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors"
                  >
                    Request Permission
                  </button>
                )}
              </div>
            </div>

            {/* Enable/Disable Notifications */}
            <div className="flex items-center justify-between bg-gray-50 p-4 rounded-lg border">
              <div>
                <h4 className="text-lg font-semibold text-gray-900">
                  Enable Notifications
                </h4>
                <p className="text-sm text-gray-600">
                  Receive notifications for boss respawns
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.enabled}
                  onChange={(e) => updateSettings({ enabled: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-14 h-7 bg-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-blue-600 shadow-lg"></div>
              </label>
            </div>

            {/* Sound Notifications */}
            <div className="flex items-center justify-between bg-gray-50 p-4 rounded-lg border">
              <div>
                <h4 className="text-lg font-semibold text-gray-900">
                  Sound Alerts
                </h4>
                <p className="text-sm text-gray-600">
                  Play sound for boss respawn and 1-minute warnings only
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.sound}
                  onChange={(e) => updateSettings({ sound: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-14 h-7 bg-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-blue-600 shadow-lg"></div>
              </label>
            </div>

            {/* Desktop Notifications */}
            <div className="flex items-center justify-between bg-gray-50 p-4 rounded-lg border">
              <div>
                <h4 className="text-lg font-semibold text-gray-900">
                  Desktop Notifications
                </h4>
                <p className="text-sm text-gray-600">
                  Show desktop notification popups
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.desktop}
                  onChange={(e) => updateSettings({ desktop: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-14 h-7 bg-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-blue-600 shadow-lg"></div>
              </label>
            </div>

            {/* Warning Times */}
            <div className="bg-gray-50 p-4 rounded-lg border">
              <h4 className="text-lg font-semibold text-gray-900 mb-3">
                Warning Times
              </h4>
              <p className="text-sm text-gray-600 mb-4">
                Get notified X minutes before boss respawn. Sound notifications only play for respawn and 1-minute warnings.
              </p>

              {/* Current warning times */}
              <div className="flex flex-wrap gap-2 mb-4">
                {settings.warningMinutes.map((minutes) => (
                  <span
                    key={minutes}
                    className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full border border-blue-200"
                  >
                    {minutes} min
                    <button
                      onClick={() => handleRemoveWarning(minutes)}
                      className="ml-2 text-blue-600 hover:text-blue-800 font-bold"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>

              {/* Add new warning time */}
              <div className="flex gap-2">
                <input
                  type="number"
                  value={warningInput}
                  onChange={(e) => setWarningInput(e.target.value)}
                  placeholder="Minutes"
                  min="1"
                  max="1440"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  onClick={handleAddWarning}
                  disabled={!warningInput || parseInt(warningInput) <= 0}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white text-sm rounded-md transition-colors font-medium"
                >
                  Add
                </button>
              </div>
            </div>
          </div>
        </div>
    </div>
  );
}





