'use client';

import { useState, useEffect, useCallback } from 'react';
import { TimerState, RealTimeMessage, TimerAction, Boss } from '@/types/boss';
import { useNotifications } from './useNotifications';
import { useRealTimeSharing } from './useRealTimeSharing';

const STORAGE_KEY = 'boss-timer-state';

export function useCollaborativeTimer() {
  const [timerState, setTimerState] = useState<TimerState>({});
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isClient, setIsClient] = useState(false);
  const [lastAction, setLastAction] = useState<{ userId: string; userName: string; action: string; timestamp: Date } | null>(null);
  
  const { settings: notificationSettings, showNotification, playNotificationSound, stopNotificationSound, isPlaying } = useNotifications();
  const { isConnected, currentUser, sendMessage, addMessageHand<PERSON>, saveRoomState, getRoomState } = useRealTimeSharing();

  // Set client flag after mount
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Load state from localStorage on mount (client-side only)
  useEffect(() => {
    if (!isClient) return;

    const saved = localStorage.getItem(STORAGE_KEY);
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        // Convert date strings back to Date objects
        const converted: TimerState = {};
        Object.keys(parsed).forEach(key => {
          converted[key] = {
            ...parsed[key],
            lastKilled: parsed[key].lastKilled ? new Date(parsed[key].lastKilled) : undefined,
            nextRespawn: parsed[key].nextRespawn ? new Date(parsed[key].nextRespawn) : undefined,
          };
        });
        setTimerState(converted);
      } catch (error) {
        console.error('Failed to parse saved timer state:', error);
      }
    }
  }, [isClient]);

  // Save state to localStorage whenever it changes (client-side only)
  useEffect(() => {
    if (!isClient) return;
    localStorage.setItem(STORAGE_KEY, JSON.stringify(timerState));
  }, [timerState, isClient]);

  // Save timer state to room database when connected
  useEffect(() => {
    if (!isConnected || !currentUser || !isClient) return;

    // Debounce the save operation to avoid too many database calls
    const timeoutId = setTimeout(() => {
      saveRoomState('timer_state', timerState);
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [timerState, isConnected, currentUser, isClient, saveRoomState]);

  // Handle real-time messages
  useEffect(() => {
    if (!isConnected) return;

    const handleRealTimeMessage = (message: RealTimeMessage) => {
      console.log('Received real-time message:', message.type, 'from:', message.userName);
      switch (message.type) {
        case 'timer_start': {
          const { bossId, killTime, respawnHours } = message.data as TimerAction;
          const killDate = new Date(killTime || message.timestamp);
          const nextRespawn = new Date(killDate.getTime() + (respawnHours || 0) * 60 * 60 * 1000);
          
          setTimerState(prev => ({
            ...prev,
            [bossId]: {
              lastKilled: killDate,
              nextRespawn,
              isActive: true,
              notificationSent: false,
            },
          }));

          setLastAction({
            userId: message.userId,
            userName: message.userName,
            action: `started timer for ${bossId}`,
            timestamp: new Date(message.timestamp),
          });
          break;
        }

        case 'timer_stop': {
          const { bossId } = message.data as TimerAction;
          setTimerState(prev => ({
            ...prev,
            [bossId]: {
              ...prev[bossId],
              isActive: false,
            },
          }));

          setLastAction({
            userId: message.userId,
            userName: message.userName,
            action: `stopped timer for ${bossId}`,
            timestamp: new Date(message.timestamp),
          });
          break;
        }

        case 'timer_reset': {
          const { bossId } = message.data as TimerAction;
          setTimerState(prev => {
            const newState = { ...prev };
            delete newState[bossId];
            return newState;
          });

          setLastAction({
            userId: message.userId,
            userName: message.userName,
            action: `reset timer for ${bossId}`,
            timestamp: new Date(message.timestamp),
          });
          break;
        }

        case 'sync_request': {
          // Send current timer state to requesting user
          if (currentUser && Object.keys(timerState).length > 0) {
            console.log('Received sync request, sending timer state:', Object.keys(timerState));
            sendMessage('sync_response', { timerState });
          } else {
            console.log('Received sync request but no timer state to share');
          }
          break;
        }

        case 'sync_response': {
          // Merge received timer state (only if we don't have data or theirs is newer)
          const { timerState: receivedState } = message.data as { timerState?: TimerState };
          if (receivedState && Object.keys(receivedState).length > 0) {
            console.log('Received sync response with timer state:', Object.keys(receivedState));
            setTimerState(prev => {
              const merged = { ...prev };
              Object.entries(receivedState).forEach(([bossId, state]: [string, unknown]) => {
                const timerState = state as { lastKilled?: string | Date; nextRespawn?: string | Date; isActive?: boolean; notificationSent?: boolean };
                const existingState = merged[bossId];
                const receivedTimestamp = new Date(timerState.lastKilled || 0);
                const existingTimestamp = new Date(existingState?.lastKilled || 0);

                // Use the newer state
                if (!existingState || receivedTimestamp > existingTimestamp) {
                  merged[bossId] = {
                    lastKilled: timerState.lastKilled ? new Date(timerState.lastKilled) : undefined,
                    nextRespawn: timerState.nextRespawn ? new Date(timerState.nextRespawn) : undefined,
                    isActive: timerState.isActive || false,
                    notificationSent: timerState.notificationSent || false,
                  };
                }
              });
              console.log('Merged timer state, now have:', Object.keys(merged));
              return merged;
            });
          } else {
            console.log('Received sync response but no timer state data');
          }
          break;
        }

        case 'full_sync': {
          // Receive complete state from host when joining
          const { timerState: receivedState, customBosses } = message.data as { timerState?: TimerState; customBosses?: unknown[] };
          if (receivedState && Object.keys(receivedState).length > 0) {
            console.log('Received full sync with timer state:', Object.keys(receivedState));
            const converted: TimerState = {};
            Object.entries(receivedState).forEach(([bossId, state]) => {
              const timerState = state as { lastKilled?: string | Date; nextRespawn?: string | Date; isActive?: boolean; notificationSent?: boolean };
              converted[bossId] = {
                lastKilled: timerState.lastKilled ? new Date(timerState.lastKilled) : undefined,
                nextRespawn: timerState.nextRespawn ? new Date(timerState.nextRespawn) : undefined,
                isActive: timerState.isActive || false,
                notificationSent: timerState.notificationSent || false,
              };
            });
            setTimerState(converted);
            console.log('Applied full sync, timer state updated');
          } else {
            console.log('Received full sync but no timer state data');
          }

          setLastAction({
            userId: message.userId,
            userName: message.userName,
            action: 'synchronized room data',
            timestamp: new Date(message.timestamp),
          });
          break;
        }

        case 'room_closed': {
          // Handle room being closed by host
          setLastAction({
            userId: message.userId,
            userName: message.userName,
            action: 'closed the room',
            timestamp: new Date(message.timestamp),
          });
          break;
        }

        case 'user_join': {
          // Send full sync to new user if we have timer data and they're not us
          if (currentUser && message.userId !== currentUser.id && Object.keys(timerState).length > 0) {
            console.log('New user joined, sending full sync with timer state:', Object.keys(timerState));
            // Small delay to ensure the new user is ready to receive messages
            setTimeout(() => {
              sendMessage('full_sync', {
                timerState,
                customBosses: [] // Will be handled by the main component
              });
            }, 1000);
          }

          setLastAction({
            userId: message.userId,
            userName: message.userName,
            action: 'joined the room',
            timestamp: new Date(message.timestamp),
          });
          break;
        }

        case 'user_leave': {
          setLastAction({
            userId: message.userId,
            userName: message.userName,
            action: 'left the room',
            timestamp: new Date(message.timestamp),
          });
          break;
        }

        case 'state_update': {
          // Handle real-time state updates from database
          const { stateType, stateData } = message.data as { stateType: string; stateData: any };
          if (stateType === 'timer_state' && stateData && Object.keys(stateData).length > 0) {
            console.log('Received state update for timer_state:', Object.keys(stateData));
            const converted: TimerState = {};
            Object.entries(stateData).forEach(([bossId, state]) => {
              const timerState = state as { lastKilled?: string | Date; nextRespawn?: string | Date; isActive?: boolean; notificationSent?: boolean };
              converted[bossId] = {
                lastKilled: timerState.lastKilled ? new Date(timerState.lastKilled) : undefined,
                nextRespawn: timerState.nextRespawn ? new Date(timerState.nextRespawn) : undefined,
                isActive: timerState.isActive || false,
                notificationSent: timerState.notificationSent || false,
              };
            });
            setTimerState(converted);
          }
          break;
        }
      }
    };

    return addMessageHandler(handleRealTimeMessage);
  }, [isConnected, currentUser, sendMessage, addMessageHandler, timerState]);

  // Load timer state from database when connecting to a room
  useEffect(() => {
    if (isConnected && currentUser && isClient) {
      console.log('Loading timer state from database for room...');

      // Load timer state from database
      getRoomState('timer_state').then(roomStates => {
        if (roomStates.length > 0) {
          const latestState = roomStates[0]; // Already sorted by updated_at desc
          const stateData = latestState.state_data;

          if (stateData && Object.keys(stateData).length > 0) {
            const converted: TimerState = {};
            Object.entries(stateData).forEach(([bossId, state]) => {
              const timerState = state as { lastKilled?: string | Date; nextRespawn?: string | Date; isActive?: boolean; notificationSent?: boolean };
              converted[bossId] = {
                lastKilled: timerState.lastKilled ? new Date(timerState.lastKilled) : undefined,
                nextRespawn: timerState.nextRespawn ? new Date(timerState.nextRespawn) : undefined,
                isActive: timerState.isActive || false,
                notificationSent: timerState.notificationSent || false,
              };
            });

            console.log('Loaded timer state from database:', Object.keys(converted));
            setTimerState(converted);
          } else {
            console.log('No timer state data in database, requesting sync from other users');
            // No saved state, request sync from other users
            setTimeout(() => {
              sendMessage('sync_request', {});
            }, 2000); // Wait a bit for other users to be ready
          }
        } else {
          console.log('No timer state records in database, requesting sync from other users');
          // No saved state, request sync from other users
          setTimeout(() => {
            sendMessage('sync_request', {});
          }, 2000); // Wait a bit for other users to be ready
        }
      }).catch(error => {
        console.error('Failed to load timer state from database:', error);
        // Fallback to requesting sync from other users
        setTimeout(() => {
          sendMessage('sync_request', {});
        }, 2000);
      });
    }
  }, [isConnected, currentUser, isClient, getRoomState, sendMessage]);

  // Update current time every second and check for notifications
  const checkNotifications = useCallback((now: Date) => {
    Object.entries(timerState).forEach(([bossId, state]) => {
      if (!state.isActive || !state.nextRespawn) return;

      const timeUntilRespawn = state.nextRespawn.getTime() - now.getTime();
      const minutesUntilRespawn = Math.floor(timeUntilRespawn / (1000 * 60));

      // Check if boss has already respawned
      if (timeUntilRespawn <= 0 && !state.notificationSent) {
        showNotification(`Boss Respawned!`, {
          body: `${bossId} has respawned and is ready to hunt!`,
          tag: `boss-respawned-${bossId}`,
        });
        playNotificationSound();

        // Mark notification as sent
        setTimerState(prev => ({
          ...prev,
          [bossId]: {
            ...prev[bossId],
            notificationSent: true,
          },
        }));
        return;
      }

      // Check warning notifications - only play sound for 1 minute warning
      notificationSettings.warningMinutes.forEach(warningMinutes => {
        if (minutesUntilRespawn === warningMinutes && !state.notificationSent) {
          showNotification(`Boss Respawn Warning`, {
            body: `${bossId} will respawn in ${warningMinutes} minutes!`,
            tag: `boss-warning-${bossId}-${warningMinutes}`,
          });
          // Only play sound for 1 minute warning and respawn
          if (warningMinutes === 1) {
            playNotificationSound();
          }
        }
      });
    });
  }, [timerState, notificationSettings.warningMinutes, showNotification, playNotificationSound]);

  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      setCurrentTime(now);

      // Check for notification triggers
      if (notificationSettings.enabled) {
        checkNotifications(now);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [notificationSettings.enabled, checkNotifications]);

  const startTimer = useCallback((bossId: string, respawnHours: number, customKillTime?: Date) => {
    const killTime = customKillTime || new Date();
    const nextRespawn = new Date(killTime.getTime() + respawnHours * 60 * 60 * 1000);

    console.log('Starting timer for boss:', bossId, 'isConnected:', isConnected);

    setTimerState(prev => ({
      ...prev,
      [bossId]: {
        lastKilled: killTime,
        nextRespawn,
        isActive: true,
        notificationSent: false,
      },
    }));

    // Send real-time update
    if (isConnected) {
      console.log('Sending timer_start message for boss:', bossId);
      sendMessage('timer_start', {
        bossId,
        killTime,
        respawnHours,
      });
    } else {
      console.log('Not connected, timer_start message not sent');
    }
  }, [isConnected, sendMessage]);

  const stopTimer = useCallback((bossId: string) => {
    setTimerState(prev => ({
      ...prev,
      [bossId]: {
        ...prev[bossId],
        isActive: false,
      },
    }));

    // Send real-time update
    if (isConnected) {
      sendMessage('timer_stop', { bossId });
    }
  }, [isConnected, sendMessage]);

  const resetTimer = useCallback((bossId: string) => {
    setTimerState(prev => {
      const newState = { ...prev };
      delete newState[bossId];
      return newState;
    });

    // Send real-time update
    if (isConnected) {
      sendMessage('timer_reset', { bossId });
    }
  }, [isConnected, sendMessage]);

  const getTimeRemaining = useCallback((bossId: string): string | null => {
    const state = timerState[bossId];
    if (!state?.isActive || !state.nextRespawn) return null;

    const now = currentTime.getTime();
    const respawnTime = state.nextRespawn.getTime();
    const timeLeft = respawnTime - now;

    if (timeLeft <= 0) return 'Respawned!';

    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  }, [timerState, currentTime]);

  const isRespawned = useCallback((bossId: string): boolean => {
    const state = timerState[bossId];
    if (!state?.isActive || !state.nextRespawn) return false;
    return state.nextRespawn.getTime() <= currentTime.getTime();
  }, [timerState, currentTime]);

  return {
    timerState,
    currentTime,
    lastAction,
    startTimer,
    stopTimer,
    resetTimer,
    getTimeRemaining,
    isRespawned,
    stopNotificationSound,
    isPlaying,
  };
}
