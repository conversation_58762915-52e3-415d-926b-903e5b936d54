import { useState, useEffect, useCallback } from 'react';
import { TimerState } from '@/types/boss';
import { useNotifications } from './useNotifications';

const STORAGE_KEY = 'boss-timer-state';

export function useTimer() {
  const [timerState, setTimerState] = useState<TimerState>({});
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isClient, setIsClient] = useState(false);
  const { settings: notificationSettings, showNotification, playNotificationSound, stopNotificationSound, isPlaying } = useNotifications();

  // Set client flag after mount
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Load state from localStorage on mount (client-side only)
  useEffect(() => {
    if (!isClient) return;

    const saved = localStorage.getItem(STORAGE_KEY);
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        // Convert date strings back to Date objects
        const converted: TimerState = {};
        Object.keys(parsed).forEach(key => {
          converted[key] = {
            ...parsed[key],
            lastKilled: parsed[key].lastKilled ? new Date(parsed[key].lastKilled) : undefined,
            nextRespawn: parsed[key].nextRespawn ? new Date(parsed[key].nextRespawn) : undefined,
          };
        });
        setTimerState(converted);
      } catch (error) {
        console.error('Failed to parse saved timer state:', error);
      }
    }
  }, [isClient]);

  // Save state to localStorage whenever it changes (client-side only)
  useEffect(() => {
    if (!isClient) return;
    localStorage.setItem(STORAGE_KEY, JSON.stringify(timerState));
  }, [timerState, isClient]);

  // Update current time every second and check for notifications
  const checkNotifications = useCallback((now: Date) => {
    Object.entries(timerState).forEach(([bossId, state]) => {
      if (!state.isActive || !state.nextRespawn) return;

      const timeUntilRespawn = state.nextRespawn.getTime() - now.getTime();
      const minutesUntilRespawn = Math.floor(timeUntilRespawn / (1000 * 60));

      // Check if boss has already respawned
      if (timeUntilRespawn <= 0 && !state.notificationSent) {
        showNotification(`Boss Respawned!`, {
          body: `${bossId} has respawned and is ready to hunt!`,
          tag: `boss-respawned-${bossId}`,
        });
        playNotificationSound();

        // Mark notification as sent
        setTimerState(prev => ({
          ...prev,
          [bossId]: {
            ...prev[bossId],
            notificationSent: true,
          },
        }));
        return;
      }

      // Check warning notifications - only play sound for 1 minute warning
      notificationSettings.warningMinutes.forEach(warningMinutes => {
        if (minutesUntilRespawn === warningMinutes && !state.notificationSent) {
          showNotification(`Boss Respawn Warning`, {
            body: `${bossId} will respawn in ${warningMinutes} minutes!`,
            tag: `boss-warning-${bossId}-${warningMinutes}`,
          });
          // Only play sound for 1 minute warning and respawn
          if (warningMinutes === 1) {
            playNotificationSound();
          }
        }
      });
    });
  }, [timerState, notificationSettings.warningMinutes, showNotification, playNotificationSound]);

  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      setCurrentTime(now);

      // Check for notification triggers
      if (notificationSettings.enabled) {
        checkNotifications(now);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [notificationSettings.enabled, checkNotifications]);

  const startTimer = useCallback((bossId: string, respawnHours: number, customKillTime?: Date) => {
    const killTime = customKillTime || new Date();
    const nextRespawn = new Date(killTime.getTime() + respawnHours * 60 * 60 * 1000);

    setTimerState(prev => ({
      ...prev,
      [bossId]: {
        lastKilled: killTime,
        nextRespawn,
        isActive: true,
        notificationSent: false,
      },
    }));
  }, []);

  const stopTimer = useCallback((bossId: string) => {
    setTimerState(prev => ({
      ...prev,
      [bossId]: {
        ...prev[bossId],
        isActive: false,
      },
    }));
  }, []);

  const resetTimer = useCallback((bossId: string) => {
    setTimerState(prev => {
      const newState = { ...prev };
      delete newState[bossId];
      return newState;
    });
  }, []);

  const getTimeRemaining = useCallback((bossId: string): string | null => {
    const state = timerState[bossId];
    if (!state?.nextRespawn || !state.isActive) return null;

    const remaining = state.nextRespawn.getTime() - currentTime.getTime();
    if (remaining <= 0) return 'RESPAWNED!';

    const hours = Math.floor(remaining / (1000 * 60 * 60));
    const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((remaining % (1000 * 60)) / 1000);

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, [timerState, currentTime]);

  const isRespawned = useCallback((bossId: string): boolean => {
    const state = timerState[bossId];
    if (!state?.nextRespawn || !state.isActive) return false;
    return currentTime.getTime() >= state.nextRespawn.getTime();
  }, [timerState, currentTime]);

  return {
    timerState,
    startTimer,
    stopTimer,
    resetTimer,
    getTimeRemaining,
    isRespawned,
    stopNotificationSound,
    isPlaying,
  };
}

