// Script to toggle live share functionality on/off
// Usage: node toggle-live-share.js [enable|disable]

import fs from 'fs';
import path from 'path';

const BOSS_TIMER_PATH = 'src/components/BossTimer.tsx';

function readFile(filePath) {
  return fs.readFileSync(filePath, 'utf8');
}

function writeFile(filePath, content) {
  fs.writeFileSync(filePath, content, 'utf8');
}

function enableLiveShare() {
  console.log('🔄 Enabling live share functionality...');
  
  let content = readFile(BOSS_TIMER_PATH);
  
  // Enable imports
  content = content.replace(
    /\/\/ import { useCollaborativeTimer } from '@\/hooks\/useCollaborativeTimer'; \/\/ TEMPORARILY DISABLED/,
    "import { useCollaborativeTimer } from '@/hooks/useCollaborativeTimer';"
  );
  
  content = content.replace(
    /\/\/ import { useCollaborativeBossManager } from '@\/hooks\/useCollaborativeBossManager'; \/\/ TEMPORARILY DISABLED/,
    "import { useCollaborativeBossManager } from '@/hooks/useCollaborativeBossManager';"
  );
  
  content = content.replace(
    /\/\/ import RealTimeShareModal from '\.\/RealTimeShareModal'; \/\/ TEMPORARILY DISABLED/,
    "import RealTimeShareModal from './RealTimeShareModal';"
  );
  
  content = content.replace(
    /\/\/ import ConnectionStatus from '\.\/ConnectionStatus'; \/\/ TEMPORARILY DISABLED/,
    "import ConnectionStatus from './ConnectionStatus';"
  );
  
  // Disable non-collaborative imports
  content = content.replace(
    /import { useTimer } from '@\/hooks\/useTimer';/,
    "// import { useTimer } from '@/hooks/useTimer'; // DISABLED FOR LIVE SHARE"
  );
  
  content = content.replace(
    /import { useBossManager } from '@\/hooks\/useBossManager';/,
    "// import { useBossManager } from '@/hooks/useBossManager'; // DISABLED FOR LIVE SHARE"
  );
  
  // Enable hooks
  content = content.replace(
    /const { timerState, startTimer, stopTimer, resetTimer, getTimeRemaining, isRespawned, stopNotificationSound, isPlaying } = useTimer\(\);/,
    "const { timerState, startTimer, stopTimer, resetTimer, getTimeRemaining, isRespawned, stopNotificationSound, isPlaying, lastAction } = useCollaborativeTimer();"
  );
  
  content = content.replace(
    /const { allBosses, customBosses, addBoss, updateBoss, deleteBoss, isCustomBoss } = useBossManager\(\);/,
    "const { allBosses, customBosses, addBoss, updateBoss, deleteBoss, isCustomBoss, lastBossAction } = useCollaborativeBossManager();"
  );
  
  // Enable state variable
  content = content.replace(
    /\/\/ const \[showRealTimeShareModal, setShowRealTimeShareModal\] = useState\(false\); \/\/ TEMPORARILY DISABLED/,
    "const [showRealTimeShareModal, setShowRealTimeShareModal] = useState(false);"
  );
  
  // Enable Live Share button
  content = content.replace(
    /\/\* Real-Time Share Button - TEMPORARILY DISABLED \*\/\s*\/\*\s*([\s\S]*?)\s*\*\//,
    "/* Real-Time Share Button */\n$1"
  );
  
  // Enable RealTimeShareModal
  content = content.replace(
    /\/\* Real-Time Share Modal - TEMPORARILY DISABLED \*\/\s*\/\*\s*([\s\S]*?)\s*\*\//,
    "/* Real-Time Share Modal */\n$1"
  );
  
  // Enable ConnectionStatus
  content = content.replace(
    /\/\* Connection Status - TEMPORARILY DISABLED \*\/\s*\/\* <ConnectionStatus lastAction={lastAction} lastBossAction={lastBossAction} \/> \*\//,
    "/* Connection Status */\n      <ConnectionStatus lastAction={lastAction} lastBossAction={lastBossAction} />"
  );
  
  writeFile(BOSS_TIMER_PATH, content);
  console.log('✅ Live share functionality enabled!');
}

function disableLiveShare() {
  console.log('🔄 Disabling live share functionality...');
  
  let content = readFile(BOSS_TIMER_PATH);
  
  // Disable imports
  content = content.replace(
    /import { useCollaborativeTimer } from '@\/hooks\/useCollaborativeTimer';/,
    "// import { useCollaborativeTimer } from '@/hooks/useCollaborativeTimer'; // TEMPORARILY DISABLED"
  );
  
  content = content.replace(
    /import { useCollaborativeBossManager } from '@\/hooks\/useCollaborativeBossManager';/,
    "// import { useCollaborativeBossManager } from '@/hooks/useCollaborativeBossManager'; // TEMPORARILY DISABLED"
  );
  
  content = content.replace(
    /import RealTimeShareModal from '\.\/RealTimeShareModal';/,
    "// import RealTimeShareModal from './RealTimeShareModal'; // TEMPORARILY DISABLED"
  );
  
  content = content.replace(
    /import ConnectionStatus from '\.\/ConnectionStatus';/,
    "// import ConnectionStatus from './ConnectionStatus'; // TEMPORARILY DISABLED"
  );
  
  // Enable non-collaborative imports
  content = content.replace(
    /\/\/ import { useTimer } from '@\/hooks\/useTimer'; \/\/ DISABLED FOR LIVE SHARE/,
    "import { useTimer } from '@/hooks/useTimer';"
  );
  
  content = content.replace(
    /\/\/ import { useBossManager } from '@\/hooks\/useBossManager'; \/\/ DISABLED FOR LIVE SHARE/,
    "import { useBossManager } from '@/hooks/useBossManager';"
  );
  
  // Disable hooks
  content = content.replace(
    /const { timerState, startTimer, stopTimer, resetTimer, getTimeRemaining, isRespawned, stopNotificationSound, isPlaying, lastAction } = useCollaborativeTimer\(\);/,
    "const { timerState, startTimer, stopTimer, resetTimer, getTimeRemaining, isRespawned, stopNotificationSound, isPlaying } = useTimer();"
  );
  
  content = content.replace(
    /const { allBosses, customBosses, addBoss, updateBoss, deleteBoss, isCustomBoss, lastBossAction } = useCollaborativeBossManager\(\);/,
    "const { allBosses, customBosses, addBoss, updateBoss, deleteBoss, isCustomBoss } = useBossManager();"
  );
  
  // Disable state variable
  content = content.replace(
    /const \[showRealTimeShareModal, setShowRealTimeShareModal\] = useState\(false\);/,
    "// const [showRealTimeShareModal, setShowRealTimeShareModal] = useState(false); // TEMPORARILY DISABLED"
  );
  
  // Disable Live Share button
  content = content.replace(
    /\/\* Real-Time Share Button \*\/\s*([\s\S]*?)(?=\s*\/\*|$)/,
    "/* Real-Time Share Button - TEMPORARILY DISABLED */\n            /* \n$1            */"
  );
  
  // Disable RealTimeShareModal
  content = content.replace(
    /\/\* Real-Time Share Modal \*\/\s*([\s\S]*?)(?=\s*\/\*|$)/,
    "/* Real-Time Share Modal - TEMPORARILY DISABLED */\n      /* \n$1      */"
  );
  
  // Disable ConnectionStatus
  content = content.replace(
    /\/\* Connection Status \*\/\s*<ConnectionStatus lastAction={lastAction} lastBossAction={lastBossAction} \/>/,
    "/* Connection Status - TEMPORARILY DISABLED */\n      /* <ConnectionStatus lastAction={lastAction} lastBossAction={lastBossAction} /> */"
  );
  
  writeFile(BOSS_TIMER_PATH, content);
  console.log('✅ Live share functionality disabled!');
}

function showStatus() {
  const content = readFile(BOSS_TIMER_PATH);
  const isEnabled = content.includes("import { useCollaborativeTimer }") && 
                   !content.includes("// import { useCollaborativeTimer }");
  
  console.log(`📊 Live Share Status: ${isEnabled ? '🟢 ENABLED' : '🔴 DISABLED'}`);
  return isEnabled;
}

function showHelp() {
  console.log(`
🛠️  Live Share Toggle Utility

Usage: node toggle-live-share.js [command]

Commands:
  enable    Enable live share functionality
  disable   Disable live share functionality  
  status    Show current status
  help      Show this help message

Examples:
  node toggle-live-share.js enable
  node toggle-live-share.js disable
  node toggle-live-share.js status
`);
}

// Main execution
const command = process.argv[2];

switch (command) {
  case 'enable':
    enableLiveShare();
    break;
  case 'disable':
    disableLiveShare();
    break;
  case 'status':
    showStatus();
    break;
  case 'help':
  default:
    showHelp();
    break;
}
